// Wallet Manager for Fees.WTF
class WalletManager {
    constructor() {
        this.isConnected = false;
        this.currentWallet = null;
        this.address = null;
        this.provider = null;
        this.web3 = null;
        
        this.supportedWallets = {
            metamask: 'MetaMask',
            walletconnect: 'WalletConnect',
            coinbase: 'Coinbase Wallet'
        };
        
        this.init();
    }

    init() {
        console.log('👛 Initializing Wallet Manager...');
        
        // Check for existing connections
        this.checkExistingConnections();
        
        console.log('✅ Wallet Manager initialized');
    }

    async checkExistingConnections() {
        // Check MetaMask
        if (window.ethereum && window.ethereum.isMetaMask) {
            try {
                const accounts = await window.ethereum.request({ method: 'eth_accounts' });
                if (accounts.length > 0) {
                    await this.connectMetaMask(false);
                }
            } catch (error) {
                console.log('No existing MetaMask connection');
            }
        }
    }

    async connect(walletType) {
        try {
            switch (walletType) {
                case 'metamask':
                    return await this.connectMetaMask();
                case 'walletconnect':
                    return await this.connectWalletConnect();
                case 'coinbase':
                    return await this.connectCoinbase();
                default:
                    throw new Error(`Unsupported wallet type: ${walletType}`);
            }
        } catch (error) {
            console.error(`Error connecting to ${walletType}:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async connectMetaMask(requestAccounts = true) {
        if (!window.ethereum || !window.ethereum.isMetaMask) {
            throw new Error('MetaMask is not installed. Please install MetaMask to continue.');
        }

        try {
            let accounts;
            if (requestAccounts) {
                accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
            } else {
                accounts = await window.ethereum.request({ method: 'eth_accounts' });
            }

            if (accounts.length === 0) {
                throw new Error('No accounts found. Please connect your MetaMask wallet.');
            }

            this.address = accounts[0];
            this.provider = window.ethereum;
            this.currentWallet = 'metamask';
            this.isConnected = true;

            // Set up event listeners
            this.setupMetaMaskListeners();

            console.log('✅ MetaMask connected:', this.address);

            return {
                success: true,
                address: this.address,
                provider: this.provider
            };

        } catch (error) {
            throw new Error(`Failed to connect MetaMask: ${error.message}`);
        }
    }

    async connectWalletConnect() {
        // Placeholder for WalletConnect integration
        // In a real implementation, you would use the WalletConnect library
        console.warn('WalletConnect integration not implemented yet');
        
        return {
            success: false,
            error: 'WalletConnect integration coming soon'
        };
    }

    async connectCoinbase() {
        // Placeholder for Coinbase Wallet integration
        // In a real implementation, you would use the Coinbase Wallet SDK
        console.warn('Coinbase Wallet integration not implemented yet');
        
        return {
            success: false,
            error: 'Coinbase Wallet integration coming soon'
        };
    }

    setupMetaMaskListeners() {
        if (!window.ethereum) return;

        // Account changed
        window.ethereum.on('accountsChanged', (accounts) => {
            if (accounts.length === 0) {
                this.disconnect();
            } else {
                this.address = accounts[0];
                if (window.feesWTFApp) {
                    window.feesWTFApp.onWalletConnected(this.address, this.provider);
                }
            }
        });

        // Chain changed
        window.ethereum.on('chainChanged', (chainId) => {
            console.log('Chain changed to:', chainId);
            // Reload the page to reset the dapp state
            window.location.reload();
        });

        // Disconnect
        window.ethereum.on('disconnect', () => {
            this.disconnect();
        });
    }

    disconnect() {
        this.isConnected = false;
        this.currentWallet = null;
        this.address = null;
        this.provider = null;
        this.web3 = null;

        console.log('👋 Wallet disconnected');
    }

    async checkConnection() {
        return {
            isConnected: this.isConnected,
            address: this.address,
            provider: this.provider,
            walletType: this.currentWallet
        };
    }

    async getBalance(address = null) {
        if (!this.provider) {
            throw new Error('No wallet connected');
        }

        const targetAddress = address || this.address;
        if (!targetAddress) {
            throw new Error('No address provided');
        }

        try {
            const balance = await this.provider.request({
                method: 'eth_getBalance',
                params: [targetAddress, 'latest']
            });

            // Convert from wei to ETH
            const balanceInETH = parseInt(balance, 16) / Math.pow(10, 18);
            return balanceInETH;

        } catch (error) {
            console.error('Error getting balance:', error);
            throw new Error('Failed to get wallet balance');
        }
    }

    async getNetwork() {
        if (!this.provider) {
            throw new Error('No wallet connected');
        }

        try {
            const chainId = await this.provider.request({ method: 'eth_chainId' });
            return {
                chainId: parseInt(chainId, 16),
                name: this.getNetworkName(parseInt(chainId, 16))
            };
        } catch (error) {
            console.error('Error getting network:', error);
            throw new Error('Failed to get network information');
        }
    }

    getNetworkName(chainId) {
        const networks = {
            1: 'Ethereum Mainnet',
            3: 'Ropsten Testnet',
            4: 'Rinkeby Testnet',
            5: 'Goerli Testnet',
            42: 'Kovan Testnet',
            137: 'Polygon Mainnet',
            80001: 'Polygon Mumbai Testnet',
            56: 'BSC Mainnet',
            97: 'BSC Testnet'
        };

        return networks[chainId] || `Unknown Network (${chainId})`;
    }

    async switchToEthereum() {
        if (!this.provider) {
            throw new Error('No wallet connected');
        }

        try {
            await this.provider.request({
                method: 'wallet_switchEthereumChain',
                params: [{ chainId: '0x1' }] // Ethereum mainnet
            });
        } catch (error) {
            console.error('Error switching to Ethereum:', error);
            throw new Error('Failed to switch to Ethereum network');
        }
    }

    async sendTransaction(transaction) {
        if (!this.provider || !this.address) {
            throw new Error('No wallet connected');
        }

        try {
            const txHash = await this.provider.request({
                method: 'eth_sendTransaction',
                params: [{
                    from: this.address,
                    ...transaction
                }]
            });

            return txHash;
        } catch (error) {
            console.error('Error sending transaction:', error);
            throw new Error('Transaction failed');
        }
    }

    async signMessage(message) {
        if (!this.provider || !this.address) {
            throw new Error('No wallet connected');
        }

        try {
            const signature = await this.provider.request({
                method: 'personal_sign',
                params: [message, this.address]
            });

            return signature;
        } catch (error) {
            console.error('Error signing message:', error);
            throw new Error('Message signing failed');
        }
    }

    // Utility methods
    isWalletInstalled(walletType) {
        switch (walletType) {
            case 'metamask':
                return !!(window.ethereum && window.ethereum.isMetaMask);
            case 'coinbase':
                return !!(window.ethereum && window.ethereum.isCoinbaseWallet);
            default:
                return false;
        }
    }

    getWalletDownloadUrl(walletType) {
        const urls = {
            metamask: 'https://metamask.io/download/',
            coinbase: 'https://wallet.coinbase.com/',
            walletconnect: 'https://walletconnect.com/'
        };

        return urls[walletType] || '#';
    }
}

// Create global wallet manager instance
window.walletManager = new WalletManager();
