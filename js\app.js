class FeesWTFApp {
    constructor() {
        this.currentView = 'dashboard';
        this.isWalletConnected = false;
        this.walletAddress = null;
        this.web3Provider = null;
        
        this.init();
    }

    async init() {
        console.log('🚀 Initializing Fees.WTF Application...');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeApp());
        } else {
            this.initializeApp();
        }
    }

    initializeApp() {
        try {
            this.initTheme();
            
            // Initialize navigation
            this.initNavigation();
            
            // Initialize wallet connection
            this.initWalletConnection();
            
            // Initialize modals
            this.initModals();
            
            // Initialize router
            if (window.router) {
                window.router.init();
            }
            
            // Initialize gas tracker
            if (window.gasTracker) {
                window.gasTracker.init();
            }
            
            // Start periodic updates
            this.startPeriodicUpdates();
            
            console.log('✅ Fees.WTF Application initialized successfully');
            
        } catch (error) {
            console.error('❌ Error initializing application:', error);
            this.showError('Failed to initialize application. Please refresh the page.');
        }
    }

    initTheme() {
        const themeToggle = document.getElementById('theme-toggle');
        const savedTheme = localStorage.getItem('fees-wtf-theme') || 'dark';
        
        document.body.setAttribute('data-theme', savedTheme);
        this.updateThemeIcon(savedTheme);
        
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                const currentTheme = document.body.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                document.body.setAttribute('data-theme', newTheme);
                localStorage.setItem('fees-wtf-theme', newTheme);
                this.updateThemeIcon(newTheme);
            });
        }
    }

    updateThemeIcon(theme) {
        const themeIcon = document.querySelector('.theme-icon');
        if (themeIcon) {
            themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
        }
    }

    initNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const route = link.getAttribute('data-route');
                if (route && window.router) {
                    window.router.navigate(route);
                }
            });
        });
    }

    initWalletConnection() {
        const connectBtn = document.getElementById('connect-wallet');
        const disconnectBtn = document.getElementById('disconnect-wallet');
        
        if (connectBtn) {
            connectBtn.addEventListener('click', () => {
                this.showConnectModal();
            });
        }
        
        if (disconnectBtn) {
            disconnectBtn.addEventListener('click', () => {
                this.disconnectWallet();
            });
        }
        
        // Check for existing wallet connection
        this.checkExistingConnection();
    }

    initModals() {
        // Connect modal
        const connectModal = document.getElementById('connect-modal-overlay');
        const closeModal = document.getElementById('close-modal');
        
        if (closeModal) {
            closeModal.addEventListener('click', () => {
                this.hideConnectModal();
            });
        }
        
        if (connectModal) {
            connectModal.addEventListener('click', (e) => {
                if (e.target === connectModal) {
                    this.hideConnectModal();
                }
            });
        }
        
        // Wallet selection
        const walletItems = document.querySelectorAll('.modal-item[data-wallet]');
        walletItems.forEach(item => {
            item.addEventListener('click', () => {
                const walletType = item.getAttribute('data-wallet');
                this.connectWallet(walletType);
            });
        });
    }

    showConnectModal() {
        const modal = document.getElementById('connect-modal-overlay');
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    hideConnectModal() {
        const modal = document.getElementById('connect-modal-overlay');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    async connectWallet(walletType) {
        try {
            console.log(`Connecting to ${walletType} wallet...`);
            
            if (window.walletManager) {
                const result = await window.walletManager.connect(walletType);
                if (result.success) {
                    this.onWalletConnected(result.address, result.provider);
                    this.hideConnectModal();
                } else {
                    this.showError(result.error || 'Failed to connect wallet');
                }
            } else {
                this.showError('Wallet manager not initialized');
            }
        } catch (error) {
            console.error('Error connecting wallet:', error);
            this.showError('Failed to connect wallet. Please try again.');
        }
    }

    async checkExistingConnection() {
        if (window.walletManager) {
            const connection = await window.walletManager.checkConnection();
            if (connection.isConnected) {
                this.onWalletConnected(connection.address, connection.provider);
            }
        }
    }

    onWalletConnected(address, provider) {
        this.isWalletConnected = true;
        this.walletAddress = address;
        this.web3Provider = provider;
        
        this.updateWalletUI();
        this.loadWalletData();
        
        console.log('✅ Wallet connected:', address);
    }

    disconnectWallet() {
        if (window.walletManager) {
            window.walletManager.disconnect();
        }
        
        this.isWalletConnected = false;
        this.walletAddress = null;
        this.web3Provider = null;
        
        this.updateWalletUI();
        
        console.log('👋 Wallet disconnected');
    }

    updateWalletUI() {
        const connectBtn = document.getElementById('connect-wallet');
        const walletInfo = document.getElementById('wallet-info');
        const walletAddress = document.getElementById('wallet-address');
        const walletBalance = document.getElementById('wallet-balance');
        
        if (this.isWalletConnected && this.walletAddress) {
            if (connectBtn) connectBtn.classList.add('hidden');
            if (walletInfo) walletInfo.classList.remove('hidden');
            
            if (walletAddress && window.utils) {
                walletAddress.textContent = window.utils.formatAddress(this.walletAddress);
            }
        } else {
            if (connectBtn) connectBtn.classList.remove('hidden');
            if (walletInfo) walletInfo.classList.add('hidden');
        }
    }

    async loadWalletData() {
        if (!this.isWalletConnected || !this.walletAddress) return;
        
        try {
            // Load ETH balance
            if (window.walletManager) {
                const balance = await window.walletManager.getBalance(this.walletAddress);
                const balanceElement = document.getElementById('wallet-balance');
                if (balanceElement && window.utils) {
                    balanceElement.textContent = `${window.utils.formatNumber(balance, 4)} ETH`;
                }
            }
        } catch (error) {
            console.error('Error loading wallet data:', error);
        }
    }

    startPeriodicUpdates() {
        // Update gas prices every 30 seconds
        setInterval(() => {
            if (window.gasTracker) {
                window.gasTracker.updateGasPrices();
            }
        }, 30000);
        
        // Update wallet data every 60 seconds
        setInterval(() => {
            if (this.isWalletConnected) {
                this.loadWalletData();
            }
        }, 60000);
    }

    showError(message) {
        console.error('Error:', message);
        
        // Create a simple error notification
        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    showSuccess(message) {
        console.log('Success:', message);
        
        // Create a simple success notification
        const notification = document.createElement('div');
        notification.className = 'success-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #44ff44;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
}

// Initialize the application
window.feesWTFApp = new FeesWTFApp();
