# Fees.WTF PHP Server Setup Guide

## Overview
This guide will help you set up the Fees.WTF application on a PHP server. The application is now configured to work with PHP and includes proper server-side headers and configurations.

## Files Modified/Added
- `index.php` - Main application file (converted from HTML with PHP headers)
- `.htaccess` - Apache server configuration for routing and security
- `secureproxy.php` - Existing proxy for API requests (already present)

## Server Requirements
- PHP 7.4 or higher
- Apache web server with mod_rewrite enabled
- SSL certificate (recommended for production)

## Installation Steps

### 1. Upload Files
Upload all files to your web server's document root or subdirectory.

### 2. Set Permissions
```bash
chmod 644 index.php
chmod 644 .htaccess
chmod 644 secureproxy.php
chmod -R 755 js/
chmod -R 755 styles/
chmod -R 755 images/
chmod -R 755 scripts/
```

### 3. Apache Configuration
Ensure your Apache server has the following modules enabled:
- mod_rewrite
- mod_headers
- mod_deflate (optional, for compression)
- mod_expires (optional, for caching)

### 4. PHP Configuration
Add these settings to your php.ini or .htaccess:
```ini
; Enable error reporting for development (disable in production)
display_errors = Off
log_errors = On

; Increase memory limit if needed
memory_limit = 256M

; Set timezone
date.timezone = "UTC"

; Enable CORS headers
always_populate_raw_post_data = -1
```

### 5. SSL Setup (Recommended)
For production, enable HTTPS by:
1. Obtaining an SSL certificate
2. Uncommenting the HTTPS redirect lines in .htaccess
3. Updating any hardcoded HTTP URLs to HTTPS

## Testing the Setup

### 1. Basic Test
Visit your domain/subdirectory in a browser. You should see the Fees.WTF dashboard.

### 2. API Proxy Test
Test the secure proxy by visiting:
`https://yourdomain.com/secureproxy.php?e=ping_proxy`

You should see "pong" as the response.

### 3. Routing Test
Test the SPA routing by visiting:
- `https://yourdomain.com/#/stake/wtf`
- `https://yourdomain.com/#/swap`
- `https://yourdomain.com/#/nft`

All routes should load the main application.

## Troubleshooting

### Common Issues

1. **404 Errors on Routes**
   - Ensure mod_rewrite is enabled
   - Check .htaccess file permissions
   - Verify Apache configuration allows .htaccess overrides

2. **CORS Errors**
   - Check that mod_headers is enabled
   - Verify CORS headers in .htaccess
   - Test with browser developer tools

3. **API Proxy Issues**
   - Check secureproxy.php permissions
   - Verify PHP curl extension is installed
   - Check server logs for errors

4. **Static Assets Not Loading**
   - Verify file permissions
   - Check browser console for 404 errors
   - Ensure paths are correct

### Server Logs
Check your server's error logs for detailed error information:
- Apache: `/var/log/apache2/error.log`
- PHP: `/var/log/php_errors.log`

## Security Considerations

1. **API Keys**: Store sensitive API keys in environment variables or secure configuration files
2. **HTTPS**: Always use HTTPS in production
3. **Headers**: Security headers are configured in .htaccess
4. **File Permissions**: Ensure proper file permissions are set
5. **Updates**: Keep PHP and server software updated

## Performance Optimization

1. **Caching**: Static asset caching is configured in .htaccess
2. **Compression**: Gzip compression is enabled for text files
3. **CDN**: Consider using a CDN for static assets
4. **PHP OPcache**: Enable PHP OPcache for better performance

## Support
For issues specific to the Fees.WTF application, check the browser console and network tab for errors. Most functionality is client-side JavaScript that communicates with external APIs.
