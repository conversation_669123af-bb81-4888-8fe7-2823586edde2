// Gas Price Tracker for Fees.WTF
class GasTracker {
    constructor() {
        this.gasData = {
            fast: 0,
            standard: 0,
            safe: 0
        };
        
        this.ethPrice = 0;
        this.ethChange = 0;
        this.chart = null;
        this.chartInitialized = false;
        this.gasHistory = [];
        
        this.init();
    }

    init() {
        console.log('⛽ Initializing Gas Tracker...');
        
        // Initialize chart when dashboard is visible
        this.initChart();
        
        // Start fetching gas prices
        this.updateGasPrices();
        this.updateETHPrice();
        
        console.log('✅ Gas Tracker initialized');
    }

    async updateGasPrices() {
        try {
            // Try multiple gas price APIs
            const gasData = await this.fetchGasPrices();
            
            if (gasData) {
                this.gasData = gasData;
                this.updateGasUI();
                this.updateGasHistory();
            }
        } catch (error) {
            console.error('Error updating gas prices:', error);
            this.showFallbackGasData();
        }
    }

    async fetchGasPrices() {
        // Try Etherscan API first
        try {
            const response = await fetch('https://api.etherscan.io/api?module=gastracker&action=gasoracle&apikey=YourEtherscanAPIKey');
            const data = await response.json();
            
            if (data.status === '1' && data.result) {
                return {
                    fast: parseInt(data.result.FastGasPrice),
                    standard: parseInt(data.result.StandardGasPrice),
                    safe: parseInt(data.result.SafeGasPrice)
                };
            }
        } catch (error) {
            console.warn('Etherscan API failed, trying alternative...');
        }

        // Try ETH Gas Station as fallback
        try {
            const response = await fetch('https://ethgasstation.info/api/ethgasAPI.json');
            const data = await response.json();
            
            return {
                fast: Math.round(data.fast / 10),
                standard: Math.round(data.average / 10),
                safe: Math.round(data.safeLow / 10)
            };
        } catch (error) {
            console.warn('ETH Gas Station API failed');
        }

        // If all APIs fail, return null
        return null;
    }

    showFallbackGasData() {
        // Show some reasonable fallback values
        this.gasData = {
            fast: 25,
            standard: 20,
            safe: 15
        };
        this.updateGasUI();
    }

    updateGasUI() {
        // Update navbar gas price
        const currentGas = document.getElementById('current-gas');
        if (currentGas) {
            currentGas.textContent = this.gasData.standard;
        }

        // Update dashboard gas cards
        const fastGas = document.getElementById('fast-gas');
        const standardGas = document.getElementById('standard-gas');
        const safeGas = document.getElementById('safe-gas');

        if (fastGas) fastGas.textContent = `${this.gasData.fast} gwei`;
        if (standardGas) standardGas.textContent = `${this.gasData.standard} gwei`;
        if (safeGas) safeGas.textContent = `${this.gasData.safe} gwei`;
    }

    async updateETHPrice() {
        try {
            const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd&include_24hr_change=true');
            const data = await response.json();
            
            if (data.ethereum) {
                this.ethPrice = data.ethereum.usd;
                this.ethChange = data.ethereum.usd_24h_change || 0;
                this.updateETHPriceUI();
            }
        } catch (error) {
            console.error('Error updating ETH price:', error);
            // Show fallback price
            this.ethPrice = 2000; // Fallback price
            this.ethChange = 0;
            this.updateETHPriceUI();
        }
    }

    updateETHPriceUI() {
        const ethPrice = document.getElementById('eth-price');
        const ethChange = document.getElementById('eth-change');

        if (ethPrice && window.utils) {
            ethPrice.textContent = window.utils.formatCurrency(this.ethPrice);
        }

        if (ethChange && window.utils) {
            const changeText = window.utils.formatPercentage(this.ethChange);
            ethChange.textContent = changeText;
            
            // Update color based on change
            ethChange.className = 'stat-change';
            if (this.ethChange > 0) {
                ethChange.classList.add('positive');
            } else if (this.ethChange < 0) {
                ethChange.classList.add('negative');
            }
        }
    }

    initChart() {
        if (this.chartInitialized) return;
        
        const chartCanvas = document.getElementById('gas-chart');
        if (!chartCanvas || !window.Chart) {
            console.warn('Chart.js not loaded or canvas not found');
            return;
        }

        const ctx = chartCanvas.getContext('2d');
        
        // Initialize with some sample data
        const labels = [];
        const fastData = [];
        const standardData = [];
        const safeData = [];
        
        // Generate last 24 hours of sample data
        for (let i = 23; i >= 0; i--) {
            const time = new Date();
            time.setHours(time.getHours() - i);
            labels.push(time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }));
            
            // Generate realistic sample data
            fastData.push(Math.floor(Math.random() * 20) + 20);
            standardData.push(Math.floor(Math.random() * 15) + 15);
            safeData.push(Math.floor(Math.random() * 10) + 10);
        }

        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Fast',
                        data: fastData,
                        borderColor: '#ff6b6b',
                        backgroundColor: 'rgba(255, 107, 107, 0.1)',
                        tension: 0.4,
                        fill: false
                    },
                    {
                        label: 'Standard',
                        data: standardData,
                        borderColor: '#4ecdc4',
                        backgroundColor: 'rgba(78, 205, 196, 0.1)',
                        tension: 0.4,
                        fill: false
                    },
                    {
                        label: 'Safe',
                        data: safeData,
                        borderColor: '#45b7d1',
                        backgroundColor: 'rgba(69, 183, 209, 0.1)',
                        tension: 0.4,
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            color: 'var(--text-color)',
                            usePointStyle: true
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'var(--border-color)'
                        },
                        ticks: {
                            color: 'var(--text-secondary)'
                        }
                    },
                    y: {
                        grid: {
                            color: 'var(--border-color)'
                        },
                        ticks: {
                            color: 'var(--text-secondary)',
                            callback: function(value) {
                                return value + ' gwei';
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });

        this.chartInitialized = true;
        console.log('📊 Gas price chart initialized');
    }

    updateGasHistory() {
        if (!this.chart) return;

        const now = new Date();
        const timeLabel = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

        // Add new data point
        this.chart.data.labels.push(timeLabel);
        this.chart.data.datasets[0].data.push(this.gasData.fast);
        this.chart.data.datasets[1].data.push(this.gasData.standard);
        this.chart.data.datasets[2].data.push(this.gasData.safe);

        // Keep only last 24 data points
        if (this.chart.data.labels.length > 24) {
            this.chart.data.labels.shift();
            this.chart.data.datasets.forEach(dataset => {
                dataset.data.shift();
            });
        }

        this.chart.update('none'); // Update without animation for performance
    }

    // Utility method to get current gas prices
    getCurrentGasPrices() {
        return { ...this.gasData };
    }

    // Utility method to estimate transaction cost
    estimateTransactionCost(gasLimit = 21000, gasPrice = null) {
        const price = gasPrice || this.gasData.standard;
        const costInWei = gasLimit * price * 1e9; // Convert gwei to wei
        const costInETH = costInWei / 1e18;
        const costInUSD = costInETH * this.ethPrice;
        
        return {
            eth: costInETH,
            usd: costInUSD,
            gwei: price
        };
    }
}

// Create global gas tracker instance
window.gasTracker = new GasTracker();
